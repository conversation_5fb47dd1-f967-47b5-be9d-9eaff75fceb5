import { useAppDispatch, useAppSelector } from '@/store/store';
import ModalWrapper from './modal-wrapper';
import { Flex, Text, Box, useColorMode } from '@chakra-ui/react';
import { GoAlert } from 'react-icons/go';
import { IoIosTrendingUp } from 'react-icons/io';
import { alertsAndOpportunities } from '@/pages/dashboard/utils/interface';
import { useNavigate } from 'react-router-dom';
import { HiSparkles } from 'react-icons/hi2';
import { Tooltip } from '@chakra-ui/react';
import { closeModal } from '@/store/reducer/modal-reducer';
import {
   setCurrentSessionID,
   setCurrentMode,
   setKpiPrompts,
} from '@/store/reducer/analytics-agent-reducer';
import { format } from 'date-fns';
import AlertCategoryImages from '@/pages/dashboard/utils/alert-images';

function AlertsOpportunitiesViewModal() {
   const { colorMode } = useColorMode();
   const navigate = useNavigate();
   const dispatch = useAppDispatch();
   const currentModal = useAppSelector((state) => state.modal);
   const { dateRange, prevRange } = useAppSelector((state) => state.kpi);

   const allAlertsAndOpportunities = (currentModal.payload?.modalProps
      ?.allAlertsAndOpportunities || {}) as Record<
      string,
      alertsAndOpportunities
   >;

   const handleNavigate = (alert: alertsAndOpportunities) => {
      const { kpiName, direction, percentage } = alert;
      const currentPeriod = `${format(new Date(dateRange.start), 'MMM d')} - ${format(new Date(dateRange.end), 'MMM d')}`;
      const previousPeriod = `${format(new Date(prevRange.start), 'MMM d')} - ${format(new Date(prevRange.end), 'MMM d')}`;
      const buildAgentPrompt = () => {
         return `
Find the root cause and analyze the key factors that led to this KPI change.

The KPI **${kpiName}** has ${direction === 'up' ? 'increased' : 'decreased'} by **${percentage}%** when comparing the period **${currentPeriod}** vs **${previousPeriod}**.
  `;
      };

      dispatch(setCurrentSessionID(''));
      dispatch(setCurrentMode('data-analyst'));
      dispatch(closeModal());
      dispatch(
         setKpiPrompts({
            displayPrompt: `Analyze why ${alert.message}  when comparing the period ${currentPeriod} vs ${previousPeriod}`,
            aiPrompt: buildAgentPrompt(),
         }),
      );

      navigate('/marco/analytics-agent');
   };

   return (
      <ModalWrapper
         heading='Alerts & Opportunities'
         overlayBgcolor='#2424241c'
         parentClassName='alerts-view'
         size='xl'
      >
         <Flex
            direction='column'
            gap={3}
            maxHeight='400px'
            overflowY='auto'
            p={2}
         >
            {Object.values(allAlertsAndOpportunities).map((alert, idx) => (
               <Flex
                  align='center'
                  gap={3}
                  background={colorMode === 'dark' ? 'gray.700' : '#f9f9f9'}
                  borderRadius='md'
                  p={3}
                  boxShadow={
                     colorMode === 'dark'
                        ? '1px 1px 10px 1px #00000033'
                        : '1px 1px 10px 1px #cccccc33'
                  }
                  key={idx}
               >
                  {alert.isPositive ? (
                     <Flex
                        align='center'
                        justify='center'
                        bg='#e8f8ec'
                        color='#2b8a3e'
                        borderRadius='full'
                        boxSize='42px'
                     >
                        <IoIosTrendingUp size={20} />
                     </Flex>
                  ) : (
                     <Flex
                        align='center'
                        justify='center'
                        bg='#ffeaea'
                        color='#e03131'
                        borderRadius='full'
                        boxSize='42px'
                        pb='1px'
                     >
                        <GoAlert size={20} />
                     </Flex>
                  )}
                  <Flex flex='1' align='center' gap={3}>
                     <AlertCategoryImages category={alert.category} />

                     <Flex flex='1' align='center' justify='space-between'>
                        <Box>
                           <Text fontSize={13} fontWeight={600}>
                              {alert.kpiName}
                           </Text>
                           <Text fontSize={12}>
                              <Text as='span'>{alert.kpiName}</Text> is{' '}
                              <Text as='span' fontWeight='600'>
                                 {alert.direction === 'up' ? 'up' : 'down'}
                              </Text>{' '}
                              by{' '}
                              <Text
                                 as='span'
                                 color={
                                    alert.isPositive ? 'green.500' : 'red.500'
                                 }
                                 fontWeight='bold'
                              >
                                 {alert.percentage}%
                              </Text>
                           </Text>
                        </Box>

                        <Tooltip label='Analyze with AI CMO' hasArrow>
                           <Flex
                              align='center'
                              gap={1}
                              px={2}
                              py={1}
                              bg='blue.50'
                              borderRadius='md'
                              cursor='pointer'
                              _hover={{ bg: 'blue.100' }}
                              onClick={() => handleNavigate(alert)}
                              minW='fit-content'
                           >
                              <HiSparkles color='#437EEB' size={13} />
                              <Text fontSize='11px' color='blue.700'>
                                 Know Why ?
                              </Text>
                           </Flex>
                        </Tooltip>
                     </Flex>
                  </Flex>
               </Flex>
            ))}
         </Flex>
      </ModalWrapper>
   );
}

export default AlertsOpportunitiesViewModal;
